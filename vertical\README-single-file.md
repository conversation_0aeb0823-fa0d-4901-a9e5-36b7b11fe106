# Vertical OpenAI Compatible API - 单文件版本

这是一个完全自包含的 Deno 单文件实现，将 Vertical API 转换为 OpenAI 兼容的 API 格式。

## ✨ 特性

- 🚀 **单文件部署** - 只需要一个 `main.ts` 文件
- 🔧 **零配置** - 所有配置都内置在代码中
- 🔑 **双重认证** - 客户端 API 密钥 + Vertical API 令牌
- 📡 **OpenAI 兼容** - 完全兼容 OpenAI API 格式
- 🌊 **流式支持** - 支持实时流式响应
- 🧠 **推理模式** - 支持思考步骤输出

## 🚀 快速开始

### 1. 安装 Deno

```bash
# Windows (PowerShell)
irm https://deno.land/install.ps1 | iex

# macOS/Linux
curl -fsSL https://deno.land/install.sh | sh
```

### 2. 配置服务

编辑 `main.ts` 文件中的配置：

```typescript
// 修改客户端 API 密钥
const VALID_CLIENT_KEYS: Set<string> = new Set([
  "sk-your-custom-key-here",
  "sk-demo-key-12345",
  "sk-test-key-67890"
]);

// 添加或修改模型配置
const MODELS_DATA = {
  "data": [
    {
      "id": "claude-3-5-sonnet-20241022",
      "vertical_model_id": "claude-3-5-sonnet-20241022",
      "vertical_model_url": "https://api.vertical.ai/v1/models/claude-3-5-sonnet-20241022",
      // ... 其他配置
    }
  ]
};
```

### 3. 启动服务

```bash
deno run --allow-net main.ts
```

服务将在 `http://localhost:8000` 启动。

## 🔑 认证方式

该服务使用双重认证机制：

1. **客户端认证** - `Authorization: Bearer <client-api-key>`
2. **Vertical API 认证** - `X-Vertical-Token: <vertical-api-token>`

## 📡 API 使用

### 获取模型列表

```bash
curl -H "Authorization: Bearer sk-your-custom-key-here" \
     http://localhost:8000/v1/models
```

### 聊天完成（非流式）

```bash
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Authorization: Bearer sk-your-custom-key-here" \
  -H "X-Vertical-Token: your-vertical-api-token" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "claude-3-5-sonnet-20241022",
    "messages": [
      {"role": "user", "content": "Hello, how are you?"}
    ]
  }'
```

### 聊天完成（流式）

```bash
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Authorization: Bearer sk-your-custom-key-here" \
  -H "X-Vertical-Token: your-vertical-api-token" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "claude-3-5-sonnet-20241022",
    "messages": [
      {"role": "user", "content": "Hello, how are you?"}
    ],
    "stream": true
  }'
```

### 推理模式

使用带 `-thinking` 后缀的模型名称：

```bash
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Authorization: Bearer sk-your-custom-key-here" \
  -H "X-Vertical-Token: your-vertical-api-token" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "claude-3-5-sonnet-20241022-thinking",
    "messages": [
      {"role": "user", "content": "Solve: 2x + 5 = 13"}
    ]
  }'
```

## 🔧 配置说明

### 客户端 API 密钥

在 `VALID_CLIENT_KEYS` 中添加您的客户端密钥：

```typescript
const VALID_CLIENT_KEYS: Set<string> = new Set([
  "sk-your-key-1",
  "sk-your-key-2",
  "sk-your-key-3"
]);
```

### 模型配置

在 `MODELS_DATA` 中配置可用模型：

```typescript
{
  "id": "model-name",                    // OpenAI 兼容的模型名称
  "vertical_model_id": "actual-model",   // Vertical API 中的实际模型 ID
  "vertical_model_url": "https://...",   // Vertical API 端点
  "output_reasoning_flag": false,        // 是否输出推理步骤
  "description": "Model description"     // 模型描述
}
```

## 🌐 部署选项

### 本地开发
```bash
deno run --allow-net main.ts
```

### Docker 部署
```dockerfile
FROM denoland/deno:1.38.0
WORKDIR /app
COPY main.ts .
EXPOSE 8000
CMD ["run", "--allow-net", "main.ts"]
```

### Deno Deploy
直接上传 `main.ts` 到 Deno Deploy 平台。

## 🔍 健康检查

```bash
curl http://localhost:8000/health
```

返回服务状态和配置信息。

## ⚠️ 注意事项

1. **安全性**: 生产环境请使用 HTTPS
2. **令牌管理**: 不要在代码中硬编码敏感令牌
3. **错误处理**: 监控日志以便调试
4. **性能**: 大量请求时考虑负载均衡

## 🐛 故障排除

### 常见错误

1. **"需要在 X-Vertical-Token 头中提供 Vertical API 令牌"**
   - 确保请求包含 `X-Vertical-Token` 头

2. **"无效的客户端 API 密钥"**
   - 检查 `Authorization` 头和 `VALID_CLIENT_KEYS` 配置

3. **"模型未找到"**
   - 检查 `MODELS_DATA` 中是否包含请求的模型

4. **网络错误**
   - 检查 Vertical API 服务状态
   - 验证 Vertical API 令牌有效性

## 📄 许可证

MIT License

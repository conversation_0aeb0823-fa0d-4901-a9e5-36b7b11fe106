@echo off
REM Vertical OpenAI Compatible API - Deno Deployment Script for Windows

echo 🚀 Starting Vertical OpenAI Compatible API Server...

REM Check if Deno is installed
deno --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Deno is not installed. Please install Deno first:
    echo    irm https://deno.land/install.ps1 ^| iex
    pause
    exit /b 1
)

REM Check if required files exist
if not exist "main.ts" (
    echo ❌ main.ts not found in current directory
    pause
    exit /b 1
)

if not exist "models.json" (
    echo ⚠️  models.json not found, creating example file...
    echo {> models.json
    echo   "models": [>> models.json
    echo     {>> models.json
    echo       "modelId": "claude-3-5-sonnet-20241022",>> models.json
    echo       "url": "https://api.vertical.ai/v1/models/claude-3-5-sonnet-20241022">> models.json
    echo     }>> models.json
    echo   ]>> models.json
    echo }>> models.json
)

if not exist "client_api_keys.json" (
    echo ⚠️  client_api_keys.json not found, creating example file...
    echo [> client_api_keys.json
    echo   "sk-your-custom-key-here">> client_api_keys.json
    echo ]>> client_api_keys.json
)

if not exist "vertical.txt" (
    echo ⚠️  vertical.txt not found, creating example file...
    echo your-vertical-auth-token-here----description> vertical.txt
    echo ❌ Please edit vertical.txt with your actual Vertical API tokens
    pause
    exit /b 1
)

echo ✅ All configuration files found
echo 🔧 Starting server with Deno...

REM Run the server
deno run --allow-net --allow-read --allow-write main.ts

pause

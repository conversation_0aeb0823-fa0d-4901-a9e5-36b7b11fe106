# Vertical OpenAI Compatible API - Deno Version

这是一个使用 Deno 实现的 Vertical API 到 OpenAI API 格式的转换服务，提供完全兼容 OpenAI API 的接口。

## 功能特性

- ✅ OpenAI 兼容的 API 接口 (`/v1/models`, `/v1/chat/completions`)
- ✅ 支持流式和非流式响应
- ✅ 对话缓存和会话管理
- ✅ 支持推理模式 (thinking steps)
- ✅ 令牌轮换和负载均衡
- ✅ 客户端 API 密钥认证
- ✅ 单文件部署，无需额外依赖

## 快速开始

### 1. 安装 Deno

```bash
# Windows (PowerShell)
irm https://deno.land/install.ps1 | iex

# macOS/Linux
curl -fsSL https://deno.land/install.sh | sh
```

### 2. 配置文件

#### models.json
配置可用的模型：
```json
{
  "models": [
    {
      "modelId": "claude-3-5-sonnet-20241022",
      "url": "https://api.vertical.ai/v1/models/claude-3-5-sonnet-20241022"
    },
    {
      "modelId": "gpt-4o",
      "url": "https://api.vertical.ai/v1/models/gpt-4o"
    }
  ]
}
```

#### client_api_keys.json
配置客户端访问密钥：
```json
[
  "sk-your-custom-key-here",
  "sk-another-key-example"
]
```

#### vertical.txt
配置 Vertical API 认证令牌（每行一个）：
```
your-vertical-auth-token-1----optional-description
your-vertical-auth-token-2----optional-description
```

### 3. 运行服务

```bash
# 直接运行
deno run --allow-net --allow-read --allow-write main.ts

# 或者使用 shebang（Unix 系统）
chmod +x main.ts
./main.ts
```

服务将在 `http://localhost:8000` 启动。

## API 使用

### 获取模型列表

```bash
curl -H "Authorization: Bearer sk-your-custom-key-here" \
     http://localhost:8000/v1/models
```

### 聊天完成（非流式）

```bash
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Authorization: Bearer sk-your-custom-key-here" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "claude-3-5-sonnet-20241022",
    "messages": [
      {"role": "user", "content": "Hello, how are you?"}
    ]
  }'
```

### 聊天完成（流式）

```bash
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Authorization: Bearer sk-your-custom-key-here" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "claude-3-5-sonnet-20241022",
    "messages": [
      {"role": "user", "content": "Hello, how are you?"}
    ],
    "stream": true
  }'
```

### 推理模式

使用带 `-thinking` 后缀的模型名称来启用推理模式：

```bash
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Authorization: Bearer sk-your-custom-key-here" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "claude-3-5-sonnet-20241022-thinking",
    "messages": [
      {"role": "user", "content": "Solve this math problem: 2x + 5 = 13"}
    ]
  }'
```

## 部署选项

### 1. 本地开发
```bash
deno run --allow-net --allow-read --allow-write main.ts
```

### 2. Docker 部署
```dockerfile
FROM denoland/deno:1.38.0

WORKDIR /app
COPY . .

EXPOSE 8000
CMD ["run", "--allow-net", "--allow-read", "--allow-write", "main.ts"]
```

### 3. Deno Deploy
直接上传 `main.ts` 到 Deno Deploy，配置环境变量或使用配置文件。

## 环境变量

可以通过环境变量覆盖默认配置：

- `PORT`: 服务端口（默认: 8000）
- `CACHE_SIZE`: 对话缓存大小（默认: 100）

## 错误处理

服务包含完整的错误处理：

- 401: 未提供或无效的 API 密钥
- 403: API 密钥无权限
- 404: 模型未找到
- 500: 内部服务器错误
- 503: 服务不可用（配置问题）

## 性能特性

- 对话缓存：自动缓存对话历史，减少重复请求
- 令牌轮换：自动轮换 Vertical API 令牌，提高可用性
- 流式响应：支持实时流式输出
- 内存管理：LRU 缓存自动清理旧对话

## 注意事项

1. 确保 Vertical API 令牌有效且有足够的配额
2. 客户端 API 密钥应该保密，不要提交到版本控制
3. 生产环境建议使用 HTTPS
4. 监控日志以便调试和性能优化

## 故障排除

### 常见问题

1. **"服务不可用: 未配置客户端 API 密钥"**
   - 检查 `client_api_keys.json` 文件是否存在且格式正确

2. **"服务不可用: 未配置 Vertical 认证令牌"**
   - 检查 `vertical.txt` 文件是否存在且包含有效令牌

3. **"模型未找到"**
   - 检查 `models.json` 文件中是否包含请求的模型

4. **连接错误**
   - 检查网络连接和 Vertical API 服务状态
   - 验证 Vertical API 令牌是否有效

## 许可证

MIT License

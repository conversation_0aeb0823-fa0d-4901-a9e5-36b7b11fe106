#!/bin/bash

# Vertical OpenAI Compatible API - 测试脚本

BASE_URL="http://localhost:8000"
CLIENT_KEY="sk-your-custom-key-here"
VERTICAL_TOKEN="your-vertical-api-token"

echo "🧪 测试 Vertical OpenAI Compatible API"
echo "=================================="

# 测试健康检查
echo ""
echo "1️⃣ 测试健康检查..."
curl -s "$BASE_URL/health" | jq '.' || echo "❌ 健康检查失败"

# 测试模型列表
echo ""
echo "2️⃣ 测试模型列表..."
curl -s -H "Authorization: Bearer $CLIENT_KEY" \
     "$BASE_URL/v1/models" | jq '.data[].id' || echo "❌ 模型列表获取失败"

# 测试聊天完成（非流式）
echo ""
echo "3️⃣ 测试聊天完成（非流式）..."
curl -s -X POST "$BASE_URL/v1/chat/completions" \
  -H "Authorization: Bearer $CLIENT_KEY" \
  -H "X-Vertical-Token: $VERTICAL_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "claude-3-5-sonnet-20241022",
    "messages": [
      {"role": "user", "content": "Say hello in one word"}
    ]
  }' | jq '.choices[0].message.content' || echo "❌ 聊天完成测试失败"

# 测试推理模式
echo ""
echo "4️⃣ 测试推理模式..."
curl -s -X POST "$BASE_URL/v1/chat/completions" \
  -H "Authorization: Bearer $CLIENT_KEY" \
  -H "X-Vertical-Token: $VERTICAL_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "claude-3-5-sonnet-20241022-thinking",
    "messages": [
      {"role": "user", "content": "What is 2+2?"}
    ]
  }' | jq '.choices[0].message.content' || echo "❌ 推理模式测试失败"

# 测试流式响应（前几行）
echo ""
echo "5️⃣ 测试流式响应（前5行）..."
curl -s -X POST "$BASE_URL/v1/chat/completions" \
  -H "Authorization: Bearer $CLIENT_KEY" \
  -H "X-Vertical-Token: $VERTICAL_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "claude-3-5-sonnet-20241022",
    "messages": [
      {"role": "user", "content": "Count from 1 to 5"}
    ],
    "stream": true
  }' | head -5 || echo "❌ 流式响应测试失败"

echo ""
echo "✅ 测试完成！"
echo ""
echo "💡 提示："
echo "   - 确保服务正在运行: deno run --allow-net main.ts"
echo "   - 修改脚本中的 VERTICAL_TOKEN 为实际令牌"
echo "   - 安装 jq 以获得更好的 JSON 格式化: apt install jq"

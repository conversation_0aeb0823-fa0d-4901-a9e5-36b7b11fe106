#!/bin/bash

# Vertical OpenAI Compatible API - Deno Deployment Script

echo "🚀 Starting Vertical OpenAI Compatible API Server..."

# Check if Deno is installed
if ! command -v deno &> /dev/null; then
    echo "❌ Deno is not installed. Please install Deno first:"
    echo "   curl -fsSL https://deno.land/install.sh | sh"
    exit 1
fi

# Check if required files exist
if [ ! -f "main.ts" ]; then
    echo "❌ main.ts not found in current directory"
    exit 1
fi

if [ ! -f "models.json" ]; then
    echo "⚠️  models.json not found, creating example file..."
    cat > models.json << 'EOF'
{
  "models": [
    {
      "modelId": "claude-3-5-sonnet-20241022",
      "url": "https://api.vertical.ai/v1/models/claude-3-5-sonnet-20241022"
    }
  ]
}
EOF
fi

if [ ! -f "client_api_keys.json" ]; then
    echo "⚠️  client_api_keys.json not found, creating example file..."
    cat > client_api_keys.json << 'EOF'
[
  "sk-your-custom-key-here"
]
EOF
fi

if [ ! -f "vertical.txt" ]; then
    echo "⚠️  vertical.txt not found, creating example file..."
    cat > vertical.txt << 'EOF'
your-vertical-auth-token-here----description
EOF
    echo "❌ Please edit vertical.txt with your actual Vertical API tokens"
    exit 1
fi

echo "✅ All configuration files found"
echo "🔧 Starting server with Deno..."

# Run the server
deno run --allow-net --allow-read --allow-write main.ts

# Vertical OpenAI Compatible API - 测试脚本 (PowerShell)

$BaseUrl = "http://localhost:8000"
$ClientKey = "sk-your-custom-key-here"
$VerticalToken = "your-vertical-api-token"

Write-Host "🧪 测试 Vertical OpenAI Compatible API" -ForegroundColor Cyan
Write-Host "==================================" -ForegroundColor Cyan

# 测试健康检查
Write-Host ""
Write-Host "1️⃣ 测试健康检查..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$BaseUrl/health" -Method Get
    Write-Host "✅ 健康检查成功" -ForegroundColor Green
    Write-Host "   状态: $($response.status)" -ForegroundColor Gray
    Write-Host "   模型数量: $($response.models)" -ForegroundColor Gray
} catch {
    Write-Host "❌ 健康检查失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试模型列表
Write-Host ""
Write-Host "2️⃣ 测试模型列表..." -ForegroundColor Yellow
try {
    $headers = @{
        "Authorization" = "Bearer $ClientKey"
    }
    $response = Invoke-RestMethod -Uri "$BaseUrl/v1/models" -Method Get -Headers $headers
    Write-Host "✅ 模型列表获取成功" -ForegroundColor Green
    Write-Host "   可用模型:" -ForegroundColor Gray
    foreach ($model in $response.data) {
        Write-Host "   - $($model.id)" -ForegroundColor Gray
    }
} catch {
    Write-Host "❌ 模型列表获取失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试聊天完成（非流式）
Write-Host ""
Write-Host "3️⃣ 测试聊天完成（非流式）..." -ForegroundColor Yellow
try {
    $headers = @{
        "Authorization" = "Bearer $ClientKey"
        "X-Vertical-Token" = $VerticalToken
        "Content-Type" = "application/json"
    }
    $body = @{
        model = "claude-3-5-sonnet-20241022"
        messages = @(
            @{
                role = "user"
                content = "Say hello in one word"
            }
        )
    } | ConvertTo-Json -Depth 3
    
    $response = Invoke-RestMethod -Uri "$BaseUrl/v1/chat/completions" -Method Post -Headers $headers -Body $body
    Write-Host "✅ 聊天完成测试成功" -ForegroundColor Green
    Write-Host "   回复: $($response.choices[0].message.content)" -ForegroundColor Gray
} catch {
    Write-Host "❌ 聊天完成测试失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试推理模式
Write-Host ""
Write-Host "4️⃣ 测试推理模式..." -ForegroundColor Yellow
try {
    $headers = @{
        "Authorization" = "Bearer $ClientKey"
        "X-Vertical-Token" = $VerticalToken
        "Content-Type" = "application/json"
    }
    $body = @{
        model = "claude-3-5-sonnet-20241022-thinking"
        messages = @(
            @{
                role = "user"
                content = "What is 2+2?"
            }
        )
    } | ConvertTo-Json -Depth 3
    
    $response = Invoke-RestMethod -Uri "$BaseUrl/v1/chat/completions" -Method Post -Headers $headers -Body $body
    Write-Host "✅ 推理模式测试成功" -ForegroundColor Green
    Write-Host "   回复: $($response.choices[0].message.content)" -ForegroundColor Gray
} catch {
    Write-Host "❌ 推理模式测试失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试认证失败
Write-Host ""
Write-Host "5️⃣ 测试认证失败..." -ForegroundColor Yellow
try {
    $headers = @{
        "Authorization" = "Bearer invalid-key"
        "Content-Type" = "application/json"
    }
    $response = Invoke-RestMethod -Uri "$BaseUrl/v1/models" -Method Get -Headers $headers
    Write-Host "❌ 认证测试失败：应该返回错误" -ForegroundColor Red
} catch {
    Write-Host "✅ 认证测试成功：正确拒绝无效密钥" -ForegroundColor Green
}

Write-Host ""
Write-Host "✅ 测试完成！" -ForegroundColor Green
Write-Host ""
Write-Host "💡 提示:" -ForegroundColor Cyan
Write-Host "   - 确保服务正在运行: deno run --allow-net main.ts" -ForegroundColor Gray
Write-Host "   - 修改脚本中的 `$VerticalToken 为实际令牌" -ForegroundColor Gray
Write-Host "   - 检查防火墙设置允许端口 8000" -ForegroundColor Gray
